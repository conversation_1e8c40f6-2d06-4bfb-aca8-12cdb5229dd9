<template>
  <div class="page-container">
    <!-- 头部导航 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h1>北航QQ身份认证系统</h1>
        </div>
        <div class="nav-actions">
          <el-button type="primary" @click="$router.push('/admin/login')">
            管理员登录
          </el-button>
        </div>
      </div>
    </el-header>

    <!-- 主要内容 -->
    <el-main class="main-content">
      <div class="hero-section">
        <div class="hero-content">
          <h2 class="hero-title">欢迎使用{{ configs.site_name }}</h2>
          <p class="hero-description">
            {{ configs.site_description }}
          </p>
        </div>
      </div>

      <!-- 认证选项 -->
      <div class="auth-options">
        <div class="options-grid">
          <!-- 本校学生 -->
          <el-card 
            class="auth-card" 
            :class="{ disabled: !configs.buaa_enabled }"
            @click="handleAuthClick('buaa')"
            shadow="hover"
          >
            <div class="card-icon">
              <el-icon size="48" color="#409EFF">
                <School />
              </el-icon>
            </div>
            <h3 class="card-title">本校学生</h3>
            <p class="card-description">
              北航在校本科生、研究生身份认证
            </p>
            <div class="card-methods">
              <el-tag size="small">SSO登录</el-tag>
              <el-tag size="small" type="success">邮箱验证</el-tag>
            </div>
            <div v-if="!configs.buaa_enabled" class="disabled-overlay">
              <span>暂未开放</span>
            </div>
          </el-card>

          <!-- 新生 -->
          <el-card 
            class="auth-card" 
            :class="{ disabled: !configs.freshman_enabled || !isInFreshmanWindow }"
            @click="handleAuthClick('freshman')"
            shadow="hover"
          >
            <div class="card-icon">
              <el-icon size="48" color="#67C23A">
                <UserFilled />
              </el-icon>
            </div>
            <h3 class="card-title">新生</h3>
            <p class="card-description">
              高考录取后的新生身份认证
            </p>
            <div class="card-methods">
              <el-tag size="small" type="warning">录取查询</el-tag>
              <el-tag size="small" type="success">邮箱验证</el-tag>
            </div>
            <div v-if="!configs.freshman_enabled" class="disabled-overlay">
              <span>暂未开放</span>
            </div>
            <div v-else-if="!isInFreshmanWindow" class="disabled-overlay">
              <span>不在开放时间</span>
            </div>
          </el-card>

          <!-- 外校学生 -->
          <el-card
            class="auth-card"
            :class="{ disabled: !configs.external_enabled }"
            @click="handleAuthClick('external')"
            shadow="hover"
          >
            <div class="card-icon">
              <el-icon size="48" color="#E6A23C">
                <Connection />
              </el-icon>
            </div>
            <h3 class="card-title">外校学生</h3>
            <p class="card-description">
              其他高校在读学生身份认证
            </p>
            <div class="card-methods">
              <el-tag size="small" type="info">学籍证明</el-tag>
              <el-tag size="small" type="success">邮箱验证</el-tag>
            </div>
            <div v-if="!configs.external_enabled" class="disabled-overlay">
              <span>暂未开放</span>
            </div>
          </el-card>

          <!-- 邀请码 -->
          <el-card
            class="auth-card"
            :class="{ disabled: !configs.invite_enabled }"
            @click="handleAuthClick('invite')"
            shadow="hover"
          >
            <div class="card-icon">
              <el-icon size="48" color="#F56C6C">
                <Ticket />
              </el-icon>
            </div>
            <h3 class="card-title">邀请码认证</h3>
            <p class="card-description">
              通过已认证用户邀请码进行认证
            </p>
            <div class="card-methods">
              <el-tag size="small" type="danger">邀请码</el-tag>
              <el-tag size="small" type="success">邮箱验证</el-tag>
            </div>
            <div v-if="!configs.invite_enabled" class="disabled-overlay">
              <span>暂未开放</span>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 系统说明 -->
      <div class="info-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <div class="info-icon">
                <el-icon size="32" color="#409EFF">
                  <Lock />
                </el-icon>
              </div>
              <h4>安全保障</h4>
              <p>采用AES-256加密，确保您的个人信息安全</p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <div class="info-icon">
                <el-icon size="32" color="#67C23A">
                  <CircleCheck />
                </el-icon>
              </div>
              <h4>快速认证</h4>
              <p>多种认证方式，快速完成身份验证</p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <div class="info-icon">
                <el-icon size="32" color="#E6A23C">
                  <Service />
                </el-icon>
              </div>
              <h4>24小时服务</h4>
              <p>全天候提供身份认证服务</p>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 使用说明 -->
      <div class="help-section">
        <el-card class="help-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>使用说明</span>
            </div>
          </template>
          <el-steps :active="4" align-center>
            <el-step title="选择身份类型" description="根据您的实际情况选择对应的认证类型"></el-step>
            <el-step title="填写基本信息" description="按要求填写真实的个人信息"></el-step>
            <el-step title="验证身份信息" description="通过邮箱、短信等方式验证身份"></el-step>
            <el-step title="等待审核" description="系统将在1-3个工作日内完成审核"></el-step>
            <el-step title="认证完成" description="审核通过后即可正常使用相关服务"></el-step>
          </el-steps>
        </el-card>
      </div>
    </el-main>

    <!-- 页脚 -->
    <el-footer class="footer">
      <div class="footer-content">
        <p>&copy; 2025 {{ configs.site_name }}</p>
        <p>如有问题请联系管理员: {{ configs.contact_email }}</p>
      </div>
    </el-footer>

    <!-- 维护提示对话框 -->
    <el-dialog
      v-model="showMaintenanceDialog"
      title="系统维护提示"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="maintenance-content">
        <el-icon size="48" color="#E6A23C">
          <Warning />
        </el-icon>
        <p>{{ maintenanceMessage }}</p>
      </div>
      <template #footer>
        <el-button type="primary" @click="showMaintenanceDialog = false">
          我知道了
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useConfigStore } from '@/stores/config'
import {
  School,
  UserFilled,
  Connection,
  Ticket,
  Lock,
  CircleCheck,
  Service,
  Warning
} from '@element-plus/icons-vue'

const router = useRouter()
const configStore = useConfigStore()

// 计算属性
const configs = computed(() => configStore.configs)

const showMaintenanceDialog = ref(false)
const maintenanceMessage = ref('')

const isInFreshmanWindow = computed(() => configStore.isInFreshmanWindow())

// 动态设置页面标题
const updatePageTitle = () => {
  if (configs.value.site_name) {
    document.title = configs.value.site_name
  }
}

// 方法
const handleAuthClick = (type) => {
  // 检查功能是否启用
  const featureMap = {
    buaa: 'buaa_enabled',
    freshman: 'freshman_enabled',
    external: 'external_enabled',
    invite: 'invite_enabled'
  }

  const featureName = featureMap[type]
  if (!configStore.isFeatureEnabled(featureName)) {
    showMaintenanceDialog.value = true
    maintenanceMessage.value = configStore.getFeatureDisabledReason(featureName)
    return
  }

  // 特殊检查新生认证的时间窗口
  if (type === 'freshman' && !isInFreshmanWindow.value) {
    showMaintenanceDialog.value = true
    maintenanceMessage.value = configStore.getFeatureDisabledReason('freshman_enabled')
    return
  }

  // 跳转到对应的认证页面
  const routes = {
    buaa: '/auth/buaa',
    freshman: '/auth/freshman',
    external: '/auth/external',
    invite: '/auth/invite'
  }

  router.push(routes[type])
}

// 监听配置变化，更新页面标题
watch(() => configs.value.site_name, (newTitle) => {
  if (newTitle) {
    document.title = newTitle
  }
}, { immediate: true })

// 检查维护模式
const checkMaintenanceMode = () => {
  if (configs.value.maintenance_mode) {
    showMaintenanceDialog.value = true
    maintenanceMessage.value = configs.value.maintenance_message || '系统正在维护中，请稍后再试'
  }
}

// 生命周期
onMounted(async () => {
  await configStore.init()
  updatePageTitle()
  checkMaintenanceMode()
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 80px;
  
  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }
  
  .logo h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.main-content {
  flex: 1;
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.hero-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 80px 20px;
  text-align: center;
  
  .hero-title {
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
  }
  
  .hero-description {
    font-size: 18px;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.auth-options {
  padding: 60px 20px;
  
  .options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
  }
}

.auth-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  
  &:hover:not(.disabled) {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }
  
  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  .card-icon {
    text-align: center;
    margin-bottom: 20px;
  }
  
  .card-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    text-align: center;
  }
  
  .card-description {
    color: #7f8c8d;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.5;
  }
  
  .card-methods {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #f56c6c;
  }
}

.info-section {
  padding: 60px 20px;
  background: #f8f9fa;
  
  .info-card {
    text-align: center;
    border: none;
    
    .info-icon {
      margin-bottom: 15px;
    }
    
    h4 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    p {
      color: #7f8c8d;
      line-height: 1.5;
    }
  }
}

.help-section {
  padding: 60px 20px;
  
  .help-card {
    max-width: 800px;
    margin: 0 auto;
    
    .card-header {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
    }
  }
}

.footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 30px 20px;
  
  .footer-content p {
    margin: 5px 0;
    
    &:first-child {
      font-weight: 600;
    }
    
    &:last-child {
      color: #bdc3c7;
      font-size: 14px;
    }
  }
}

.maintenance-content {
  text-align: center;
  padding: 20px;
  
  .el-icon {
    margin-bottom: 15px;
  }
  
  p {
    font-size: 16px;
    color: #606266;
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section {
    padding: 40px 20px;
    
    .hero-title {
      font-size: 28px;
    }
    
    .hero-description {
      font-size: 16px;
    }
  }
  
  .auth-options {
    padding: 40px 20px;
    
    .options-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
  
  .info-section,
  .help-section {
    padding: 40px 20px;
  }
  
  .header .header-content {
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px;
    
    .logo h1 {
      font-size: 20px;
    }
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 24px !important;
  }
  
  .hero-description {
    font-size: 14px !important;
  }
  
  .auth-card .card-title {
    font-size: 18px;
  }
}
</style>