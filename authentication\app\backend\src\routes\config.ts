/**
 * 配置管理路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { configService } from '@/services/ConfigService';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin, optionalAuth } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import { ConfigUpdateSchema } from '@/schemas/auth';
import { ConfigUpdateInput } from '@/types';

export async function configRoutes(fastify: FastifyInstance) {
  // 获取公开配置（不需要认证）
  fastify.get('/public', {
    preHandler: [optionalAuth],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 获取所有配置，然后过滤出公开配置
        const allConfigs = await configService.getAllConfigs();

        // 定义公开配置的键映射（后端键 -> 前端键）
        const publicKeyMappings = {
          'auth.buaa_enabled': 'buaa_enabled',
          'auth.buaa_sso_enabled': 'buaa_sso_enabled',
          'auth.buaa_email_enabled': 'buaa_email_enabled',
          'auth.freshman_enabled': 'freshman_enabled',
          'auth.external_enabled': 'external_enabled',
          'auth.invite_enabled': 'invite_enabled',
          'auth.freshman_available': 'freshman_available',
          'auth.freshman_maintenance_windows': 'freshman_maintenance_windows',
          'system.site_name': 'site_name',
          'system.site_description': 'site_description',
          'system.contact_email': 'contact_email',
          'system.maintenance_mode': 'maintenance_mode',
          'system.maintenance_message': 'maintenance_message'
        };

        const publicConfigs: Record<string, any> = {};
        for (const [backendKey, frontendKey] of Object.entries(publicKeyMappings)) {
          if (backendKey in allConfigs) {
            publicConfigs[frontendKey] = allConfigs[backendKey];
          } else {
            // 如果配置不存在，使用默认值
            const defaultConfig = await configService.getConfig(backendKey);
            if (defaultConfig !== null) {
              publicConfigs[frontendKey] = defaultConfig;
            }
          }
        }

        return reply.code(200).send({
          success: true,
          data: publicConfigs,
        });
      } catch (error) {
        logger.error('Get public configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取公开配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取所有配置（需要管理员权限）
  fastify.get('/', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const configs = await configService.getAllConfigs();

        return reply.code(200).send({
          success: true,
          data: configs,
        });
      } catch (error) {
        logger.error('Get all configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个配置项
  fastify.get<{ Params: { key: string } }>('/:key', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Params: { key: string } }>, reply: FastifyReply) => {
      try {
        const { key } = request.params;

        const config = await configService.getConfigItem(key);

        if (!config) {
          return reply.code(404).send({
            success: false,
            message: '配置项不存在',
            error: 'CONFIG_NOT_FOUND',
          });
        }

        return reply.code(200).send({
          success: true,
          data: config,
        });
      } catch (error) {
        logger.error('Get config error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取配置项失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 批量更新配置 (POST方式)
  fastify.post<{ Body: ConfigUpdateInput }>('/batch-update', {
    schema: ConfigUpdateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: ConfigUpdateInput }>, reply: FastifyReply) => {
      try {
        const { configs } = request.body;
        const admin = request.user as any;

        // 验证配置项
        const validationResult = await configService.validateConfigs(configs);
        if (!validationResult.valid) {
          return reply.code(400).send({
            success: false,
            message: '配置验证失败',
            error: 'CONFIG_VALIDATION_ERROR',
            details: validationResult.errors,
          });
        }

        // 批量更新配置
        const result = await configService.batchUpdateConfigs(configs);

        if (!result.success) {
          return reply.code(400).send({
            success: false,
            message: '配置更新失败',
            error: 'CONFIG_UPDATE_ERROR',
            details: result.errors,
          });
        }

        // 记录安全日志
        SecurityLogger.logSecurityEvent(
          'config_batch_update',
          {
            adminId: admin.id,
            configCount: configs.length,
            configKeys: configs.map(c => c.key),
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '配置更新成功',
          data: {
            updated: result.updated,
            failed: result.failed,
          },
        });
      } catch (error) {
        logger.error('Batch update configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '配置更新失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 更新配置 (PUT方式，保持向后兼容)
  fastify.put<{ Body: ConfigUpdateInput }>('/', {
    schema: ConfigUpdateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: ConfigUpdateInput }>, reply: FastifyReply) => {
      try {
        const { configs } = request.body;
        const admin = request.user as any;

        // 验证配置项
        const validationResult = await configService.validateConfigs(configs);
        if (!validationResult.valid) {
          return reply.code(400).send({
            success: false,
            message: '配置验证失败',
            error: 'CONFIG_VALIDATION_ERROR',
            details: validationResult.errors,
          });
        }

        // 批量更新配置
        const result = await configService.batchUpdateConfigs(configs);

        if (!result.success) {
          return reply.code(400).send({
            success: false,
            message: '配置更新失败',
            error: 'CONFIG_UPDATE_ERROR',
            details: result.errors,
          });
        }

        // 记录安全日志
        SecurityLogger.logSecurityEvent(
          'config_updated',
          {
            adminId: admin.id,
            configKeys: configs.map(c => c.key),
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '配置更新成功',
          data: {
            updated: result.updated,
            failed: result.failed,
          },
        });
      } catch (error) {
        logger.error('Update configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '更新配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 重置配置到默认值
  fastify.post<{ Params: { key: string } }>('/:key/reset', {
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { key: string } }>, reply: FastifyReply) => {
      try {
        const { key } = request.params;
        const admin = request.user as any;

        const result = await configService.resetConfigToDefault(key);

        if (!result.success) {
          return reply.code(400).send({
            success: false,
            message: result.message || '重置配置失败',
            error: 'CONFIG_RESET_ERROR',
          });
        }

        // 记录审计日志
        await configService.logConfigChanges(
          [{ key, value: result.defaultValue, description: '重置到默认值' }],
          admin.adminId,
          request.ip,
          request.headers['user-agent'] || ''
        );

        SecurityLogger.logSecurityEvent(
          'config_reset',
          {
            adminId: admin.adminId,
            configKey: key,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '配置重置成功',
          data: {
            key,
            value: result.defaultValue,
          },
        });
      } catch (error) {
        logger.error('Reset config error:', error);
        return reply.code(500).send({
          success: false,
          message: '重置配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取配置定义（用于前端表单生成）
  fastify.get('/definitions', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const definitions = await configService.getConfigDefinitions();

        return reply.code(200).send({
          success: true,
          data: definitions,
        });
      } catch (error) {
        logger.error('Get config definitions error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取配置定义失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 导出配置
  fastify.get('/export', {
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const configs = await configService.getAllConfigs();
        const admin = request.user as any;

        // 记录导出操作
        SecurityLogger.logSecurityEvent(
          'config_exported',
          {
            adminId: admin.adminId,
            configCount: configs.length,
            ip: request.ip,
          },
          'low'
        );

        // 设置响应头
        reply.header('Content-Type', 'application/json');
        reply.header('Content-Disposition', `attachment; filename="config-export-${new Date().toISOString().split('T')[0]}.json"`);

        return reply.code(200).send({
          exportTime: new Date().toISOString(),
          exportedBy: admin.adminId,
          configs,
        });
      } catch (error) {
        logger.error('Export configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '导出配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 导入配置
  fastify.post('/import', {
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const importData = request.body as any;
        const admin = request.user as any;

        if (!importData.configs || !Array.isArray(importData.configs)) {
          return reply.code(400).send({
            success: false,
            message: '导入数据格式错误',
            error: 'INVALID_IMPORT_FORMAT',
          });
        }

        // 验证导入的配置
        const validationResult = await configService.validateConfigs(importData.configs);
        if (!validationResult.valid) {
          return reply.code(400).send({
            success: false,
            message: '导入配置验证失败',
            error: 'CONFIG_VALIDATION_ERROR',
            details: validationResult.errors,
          });
        }

        // 批量更新配置
        const result = await configService.batchUpdateConfigs(importData.configs);

        if (!result.success) {
          return reply.code(400).send({
            success: false,
            message: '导入配置失败',
            error: 'CONFIG_IMPORT_ERROR',
            details: result.errors,
          });
        }

        // 记录安全日志
        SecurityLogger.logSecurityEvent(
          'config_imported',
          {
            adminId: admin.id,
            configCount: importData.configs.length,
            updated: result.updated,
            failed: result.failed,
            ip: request.ip,
          },
          'high'
        );

        return reply.code(200).send({
          success: true,
          message: '配置导入成功',
          data: {
            imported: result.updated,
            failed: result.failed,
            total: importData.configs.length,
          },
        });
      } catch (error) {
        logger.error('Import configs error:', error);
        return reply.code(500).send({
          success: false,
          message: '导入配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 刷新配置缓存
  fastify.post('/refresh-cache', {
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const admin = request.user as any;

        await configService.refreshCache();

        SecurityLogger.logSecurityEvent(
          'config_cache_refreshed',
          {
            adminId: admin.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(200).send({
          success: true,
          message: '配置缓存刷新成功',
        });
      } catch (error) {
        logger.error('Refresh config cache error:', error);
        return reply.code(500).send({
          success: false,
          message: '刷新配置缓存失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取配置变更历史
  fastify.get('/history', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const history = await configService.getConfigHistory();

        return reply.code(200).send({
          success: true,
          data: history,
        });
      } catch (error) {
        logger.error('Get config history error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取配置历史失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
