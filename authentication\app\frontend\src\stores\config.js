import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/utils/request'

export const useConfigStore = defineStore('config', () => {
  // 状态
  const configs = ref({
    buaa_enabled: true,
    buaa_sso_enabled: true,
    buaa_email_enabled: true,
    freshman_enabled: true,
    external_enabled: true,
    invite_enabled: true,
    freshman_available: [],
    freshman_maintenance_windows: []
  })
  
  const loading = ref(false)
  const lastUpdated = ref(null)

  // 获取公开配置
  const loadPublicConfigs = async () => {
    if (loading.value) return
    
    loading.value = true
    try {
      const response = await request.get('/api/config/public')
      if (response.success) {
        const configData = response.data
        configs.value = {
          buaa_enabled: configData.buaa_enabled !== false,
          buaa_sso_enabled: configData.buaa_sso_enabled !== false,
          buaa_email_enabled: configData.buaa_email_enabled !== false,
          freshman_enabled: configData.freshman_enabled !== false,
          external_enabled: configData.external_enabled !== false,
          invite_enabled: configData.invite_enabled !== false,
          freshman_available: configData.freshman_available || [],
          freshman_maintenance_windows: configData.freshman_maintenance_windows || [],
          site_name: configData.site_name || '北航QQ身份认证系统',
          site_description: configData.site_description || '北京航空航天大学QQ身份认证系统',
          contact_email: configData.contact_email || '<EMAIL>',
          maintenance_mode: configData.maintenance_mode || false,
          maintenance_message: configData.maintenance_message || '系统正在维护中，请稍后再试'
        }
        lastUpdated.value = new Date()
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 检查功能是否启用
  const isFeatureEnabled = (featureName) => {
    return configs.value[featureName] === true
  }

  // 检查新生认证是否在开放时间内
  const isInFreshmanWindow = () => {
    if (!configs.value.freshman_enabled) return false
    
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5)
    
    // 检查是否在可用日期范围内
    const availableDates = configs.value.freshman_available
    if (availableDates.length === 2) {
      if (today < availableDates[0] || today > availableDates[1]) {
        return false
      }
    }
    
    // 检查是否在维护时间窗口内
    const maintenanceWindows = configs.value.freshman_maintenance_windows || []
    for (const window of maintenanceWindows) {
      if (window.includes('-')) {
        const [startTime, endTime] = window.split('-')
        if (currentTime >= startTime && currentTime <= endTime) {
          return false
        }
      }
    }
    
    return true
  }

  // 获取功能禁用原因
  const getFeatureDisabledReason = (featureName) => {
    if (!isFeatureEnabled(featureName)) {
      const featureNames = {
        buaa_enabled: '本校学生认证',
        freshman_enabled: '新生认证',
        external_enabled: '外校学生认证',
        invite_enabled: '邀请码认证'
      }
      return `${featureNames[featureName]}功能已关闭`
    }
    
    if (featureName === 'freshman_enabled' && !isInFreshmanWindow()) {
      const availableDates = configs.value.freshman_available
      let message = '新生认证功能不在开放时间内'
      
      if (availableDates.length === 2) {
        message += `，开放时间：${availableDates[0]} 至 ${availableDates[1]}`
      }
      
      const maintenanceWindows = configs.value.freshman_maintenance_windows
      if (maintenanceWindows.length > 0) {
        message += `，每日维护时间：${maintenanceWindows.join(', ')}`
      }
      
      return message
    }
    
    return null
  }

  // 初始化时加载配置
  const init = async () => {
    await loadPublicConfigs()
  }

  return {
    configs,
    loading,
    lastUpdated,
    loadPublicConfigs,
    isFeatureEnabled,
    isInFreshmanWindow,
    getFeatureDisabledReason,
    init
  }
})
