/**
 * 认证路由 - 处理用户认证相关请求
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authService } from '@/services/AuthService';
import { verificationService } from '@/services/VerificationService';
import { configService } from '@/services/ConfigService';
import { logger, SecurityLogger } from '@/utils/logger';
import { 
  AuthenticateUserSchema,
  AdminLoginSchema,
  VerificationCodeSchema,
  VerificationCodeVerifySchema,
  RefreshTokenSchema,
} from '@/schemas/auth';
import {
  AuthenticateUserInput,
  AdminLoginInput,
  VerificationCodeInput,
  VerificationCodeVerifyInput,
  RefreshTokenInput,
} from '@/types';

export async function authRoutes(fastify: FastifyInstance) {
  // 本校学生SSO认证
  fastify.post('/sso', {
    schema: {
      body: {
        type: 'object',
        required: ['qq', 'username', 'password'],
        properties: {
          qq: { type: 'string', pattern: '^\\d{5,12}$' },
          username: { type: 'string', minLength: 1 },
          password: { type: 'string', minLength: 1 }
        }
      }
    },
    handler: async (request: FastifyRequest<{ Body: { qq: string; username: string; password: string } }>, reply: FastifyReply) => {
      try {
        const { qq, username, password } = request.body;
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'] || '';

        // 记录认证尝试
        SecurityLogger.logAuthAttempt(qq, ipAddress, userAgent, 'sso');

        // 检查配置是否启用本校学生认证
        const configs = await configService.getAllConfigs();
        if (!configs['auth.buaa_enabled']) {
          return reply.code(403).send({
            success: false,
            message: '本校学生认证功能已关闭',
            error: 'FEATURE_DISABLED',
          });
        }

        // TODO: 实现实际的SSO认证逻辑
        // 这里应该调用北航SSO服务进行验证
        // 暂时返回成功响应用于测试
        logger.info(`SSO认证请求: QQ=${qq}, Username=${username}`);

        return reply.send({
          success: true,
          message: 'SSO认证成功',
          data: {
            qq,
            username,
            verified: true
          }
        });

      } catch (error) {
        logger.error('SSO认证错误:', error);
        return reply.code(500).send({
          success: false,
          message: '认证服务暂时不可用',
          error: 'INTERNAL_SERVER_ERROR',
        });
      }
    }
  });

  // 用户认证
  fastify.post<{ Body: AuthenticateUserInput }>('/authenticate', {
    schema: AuthenticateUserSchema,
    handler: async (request: FastifyRequest<{ Body: AuthenticateUserInput }>, reply: FastifyReply) => {
      try {
        const { qq, verificationData } = request.body;
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'] || '';

        // 基本验证
        if (!qq || !verificationData) {
          return reply.code(400).send({
            success: false,
            message: '请提供完整的认证信息',
            error: 'MISSING_REQUIRED_FIELDS',
          });
        }

        // QQ号格式验证
        if (!/^\d{5,12}$/.test(qq)) {
          return reply.code(400).send({
            success: false,
            message: 'QQ号格式不正确',
            error: 'INVALID_QQ_FORMAT',
          });
        }

        const result = await authService.authenticateUser(qq, verificationData, ipAddress, userAgent);
        
        if (result.success) {
          return reply.code(200).send(result);
        } else {
          return reply.code(400).send(result);
        }
      } catch (error) {
        logger.error('User authentication error:', error);
        return reply.code(500).send({
          success: false,
          message: '认证过程中发生错误',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 管理员登录
  fastify.post<{ Body: AdminLoginInput }>('/admin/login', {
    schema: AdminLoginSchema,
    handler: async (request: FastifyRequest<{ Body: AdminLoginInput }>, reply: FastifyReply) => {
      try {
        const loginData = request.body;
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'] || '';

        const result = await authService.authenticateAdmin(loginData, ipAddress, userAgent);
        
        if (result.success) {
          return reply.code(200).send(result);
        } else {
          return reply.code(401).send(result);
        }
      } catch (error) {
        logger.error('Admin login error:', error);
        return reply.code(500).send({
          success: false,
          message: '登录过程中发生错误',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 发送验证码
  fastify.post<{ Body: VerificationCodeInput }>('/verification/send', {
    schema: VerificationCodeSchema,
    handler: async (request: FastifyRequest<{ Body: VerificationCodeInput }>, reply: FastifyReply) => {
      try {
        const input = request.body;
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'] || '';

        // 验证目标格式
        if (!verificationService.validateTarget(input.target, input.type)) {
          return reply.code(400).send({
            success: false,
            message: `${input.type === 'email' ? '邮箱' : '手机号'}格式不正确`,
            error: 'INVALID_TARGET_FORMAT',
          });
        }

        const result = await verificationService.sendVerificationCode(input, ipAddress, userAgent);
        
        if (result.success) {
          return reply.code(200).send(result);
        } else {
          return reply.code(400).send(result);
        }
      } catch (error) {
        logger.error('Send verification code error:', error);
        return reply.code(500).send({
          success: false,
          message: '发送验证码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 验证验证码
  fastify.post<{ Body: VerificationCodeVerifyInput }>('/verification/verify', {
    schema: VerificationCodeVerifySchema,
    handler: async (request: FastifyRequest<{ Body: VerificationCodeVerifyInput }>, reply: FastifyReply) => {
      try {
        const input = request.body;

        const result = await verificationService.verifyCode(input);
        
        if (result.success) {
          return reply.code(200).send(result);
        } else {
          return reply.code(400).send(result);
        }
      } catch (error) {
        logger.error('Verify code error:', error);
        return reply.code(500).send({
          success: false,
          message: '验证码验证失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 刷新令牌
  fastify.post<{ Body: RefreshTokenInput }>('/refresh', {
    schema: RefreshTokenSchema,
    handler: async (request: FastifyRequest<{ Body: RefreshTokenInput }>, reply: FastifyReply) => {
      try {
        const { refreshToken } = request.body;

        if (!refreshToken) {
          return reply.code(400).send({
            success: false,
            message: '刷新令牌不能为空',
            error: 'MISSING_REFRESH_TOKEN',
          });
        }

        const result = await authService.refreshToken(refreshToken);
        
        if (result) {
          return reply.code(200).send({
            success: true,
            token: result.accessToken,
            refreshToken: result.refreshToken,
            message: '令牌刷新成功',
          });
        } else {
          return reply.code(401).send({
            success: false,
            message: '刷新令牌无效或已过期',
            error: 'INVALID_REFRESH_TOKEN',
          });
        }
      } catch (error) {
        logger.error('Refresh token error:', error);
        return reply.code(500).send({
          success: false,
          message: '令牌刷新失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 登出
  fastify.post('/logout', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as any;
        const isAdmin = 'adminId' in user;
        const userId = isAdmin ? user.adminId : user.userId;

        await authService.logout(userId, isAdmin);

        return reply.code(200).send({
          success: true,
          message: '登出成功',
        });
      } catch (error) {
        logger.error('Logout error:', error);
        return reply.code(500).send({
          success: false,
          message: '登出失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取认证类型配置
  fastify.get('/types', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const [buaaEnabled, freshmanEnabled, externalEnabled, inviteEnabled] = await Promise.all([
          configService.getBooleanConfig('auth.buaa_enabled', true),
          configService.getBooleanConfig('auth.freshman_enabled', true),
          configService.getBooleanConfig('auth.external_enabled', true),
          configService.getBooleanConfig('auth.invite_enabled', true),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            buaa: buaaEnabled,
            freshman: freshmanEnabled,
            external: externalEnabled,
            invite: inviteEnabled,
          },
        });
      } catch (error) {
        logger.error('Get auth types error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取认证类型配置失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取用户信息
  fastify.get('/me', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as any;
        const isAdmin = 'adminId' in user;

        if (isAdmin) {
          const admin = await authService.getAdminById(user.adminId);
          if (!admin) {
            return reply.code(404).send({
              success: false,
              message: '管理员不存在',
              error: 'ADMIN_NOT_FOUND',
            });
          }

          return reply.code(200).send({
            success: true,
            data: {
              id: admin.id,
              username: admin.username,
              role: admin.role,
              type: 'admin',
            },
          });
        } else {
          const userData = await authService.getUserById(user.userId);
          if (!userData) {
            return reply.code(404).send({
              success: false,
              message: '用户不存在',
              error: 'USER_NOT_FOUND',
            });
          }

          return reply.code(200).send({
            success: true,
            data: {
              id: userData.id,
              qq: userData.qq,
              realName: userData.realName,
              school: userData.school,
              category: userData.category,
              status: userData.status,
              type: 'user',
            },
          });
        }
      } catch (error) {
        logger.error('Get user info error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取用户信息失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 验证令牌
  fastify.get('/verify', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        return reply.code(200).send({
          success: true,
          message: '令牌有效',
          data: request.user,
        });
      } catch (error) {
        logger.error('Verify token error:', error);
        return reply.code(500).send({
          success: false,
          message: '令牌验证失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });


}
