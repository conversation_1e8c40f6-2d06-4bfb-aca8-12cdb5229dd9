import { FastifyRequest, FastifyReply } from 'fastify';
import { configService } from '../services/ConfigService';
import { logger } from '../utils/logger';

/**
 * 维护模式中间件
 * 检查系统是否处于维护模式，如果是则阻止非管理员访问
 */
export async function maintenanceMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 检查是否是管理员路径或公开路径
    const isAdminPath = request.url.startsWith('/api/admin') ||
                       request.url.startsWith('/api/config') ||
                       request.url.startsWith('/api/auth/admin');

    // 检查是否是用户认证相关的API路径
    const isUserAuthPath = request.url.startsWith('/api/auth/') &&
                          !request.url.startsWith('/api/auth/admin');

    // 管理员路径和非用户认证路径直接跳过维护检查
    if (isAdminPath || !isUserAuthPath) {
      return;
    }

    // 检查维护模式
    const maintenanceMode = await configService.getConfig('system.maintenance_mode', false);

    if (maintenanceMode) {
      // 对于用户认证相关的API，直接返回维护模式错误
      const maintenanceMessage = await configService.getConfig(
        'system.maintenance_message',
        '系统正在维护中，请稍后再试'
      );

      return reply.code(503).send({
        success: false,
        message: maintenanceMessage,
        error: 'MAINTENANCE_MODE',
        data: {
          maintenance_mode: true,
          maintenance_message: maintenanceMessage
        }
      });
    }
  } catch (error) {
    logger.error('Maintenance middleware error:', error);
    // 如果检查维护模式失败，继续处理请求（避免因为配置问题导致系统完全不可用）
  }
}

/**
 * 注册维护模式中间件到Fastify实例
 */
export function registerMaintenanceMiddleware(fastify: any) {
  fastify.addHook('preHandler', maintenanceMiddleware);
}
