import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import { ElMessage } from 'element-plus'

// 路由组件
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/Login.vue')
const Register = () => import('@/views/Register.vue')
const BuaaAuth = () => import('@/views/auth/BuaaAuth.vue')
const FreshmanAuth = () => import('@/views/auth/FreshmanAuth.vue')
const ExternalAuth = () => import('@/views/auth/ExternalAuth.vue')
const InviteAuth = () => import('@/views/auth/InviteAuth.vue')
const Success = () => import('@/views/Success.vue')
const AdminLayout = () => import('@/layouts/AdminLayout.vue')
const AdminLogin = () => import('@/views/admin/Login.vue')
const Dashboard = () => import('@/views/admin/Dashboard.vue')
const UserManagement = () => import('@/views/admin/UserManagement.vue')
const PendingUsers = () => import('@/views/admin/PendingUsers.vue')
const SystemConfig = () => import('@/views/admin/SystemConfig.vue')
const AuditLogs = () => import('@/views/admin/AuditLogs.vue')
const Reports = () => import('@/views/admin/Reports.vue')
// 静态导入维护页面避免动态导入问题
import Maintenance from '@/views/Maintenance.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '北航QQ身份认证系统',
      requiresAuth: false
    }
  },

  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '身份认证',
      requiresAuth: false
    }
  },
  {
    path: '/auth/buaa',
    name: 'BuaaAuth',
    component: BuaaAuth,
    meta: {
      title: '本校学生认证',
      requiresAuth: false,
      requiresFeature: 'buaa_enabled'
    }
  },
  {
    path: '/auth/freshman',
    name: 'FreshmanAuth',
    component: FreshmanAuth,
    meta: {
      title: '新生认证',
      requiresAuth: false,
      requiresFeature: 'freshman_enabled'
    }
  },
  {
    path: '/auth/external',
    name: 'ExternalAuth',
    component: ExternalAuth,
    meta: {
      title: '外校学生认证',
      requiresAuth: false,
      requiresFeature: 'external_enabled'
    }
  },
  {
    path: '/auth/invite',
    name: 'InviteAuth',
    component: InviteAuth,
    meta: {
      title: '邀请码认证',
      requiresAuth: false,
      requiresFeature: 'invite_enabled'
    }
  },
  {
    path: '/success',
    name: 'Success',
    component: Success,
    meta: {
      title: '认证成功',
      requiresAuth: false
    }
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: {
      title: '管理员登录',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: {
      requiresAuth: true,
      requiresAdmin: true
    },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '管理面板',
          icon: 'DataBoard'
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: UserManagement,
        meta: {
          title: '用户管理',
          icon: 'User',
          permission: 'user.view'
        }
      },
      {
        path: 'pending',
        name: 'PendingUsers',
        component: PendingUsers,
        meta: {
          title: '待审核用户',
          icon: 'Clock',
          permission: 'pending.view'
        }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: SystemConfig,
        meta: {
          title: '系统配置',
          icon: 'Setting',
          permission: 'config.edit'
        }
      },
      {
        path: 'logs',
        name: 'AuditLogs',
        component: AuditLogs,
        meta: {
          title: '审计日志',
          icon: 'Document',
          permission: 'logs.view'
        }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: Reports,
        meta: {
          title: '统计报告',
          icon: 'PieChart',
          permission: 'reports.view'
        }
      }
    ]
  },
  {
    path: '/maintenance',
    name: 'Maintenance',
    component: Maintenance,
    meta: {
      title: '系统维护中',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const configStore = useConfigStore()

  // 确保配置已加载
  if (!configStore.lastUpdated) {
    await configStore.init()
  }

  // 设置页面标题
  if (to.meta.title) {
    const siteName = configStore.configs.site_name || '北航QQ身份认证系统'
    document.title = `${to.meta.title} - ${siteName}`
  }

  // 检查维护模式（只拦截用户认证相关路由）
  const isAdminRoute = to.path.startsWith('/admin')
  const isMaintenancePage = to.name === 'Maintenance'
  const isAuthRoute = to.path.startsWith('/auth/') || to.name === 'Register'
  const isHomePage = to.name === 'Home'

  // 只对用户认证相关的路由进行维护模式检查
  if ((isAuthRoute || isHomePage) && !isAdminRoute && !isMaintenancePage && configStore.configs.maintenance_mode) {
    // 重定向到维护页面
    next({
      name: 'Maintenance',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 检查功能是否启用
  if (to.meta.requiresFeature) {

    const featureName = to.meta.requiresFeature

    // 检查功能是否启用
    if (!configStore.isFeatureEnabled(featureName)) {
      ElMessage.error(configStore.getFeatureDisabledReason(featureName))
      next({ name: 'NotFound' })
      return
    }

    // 特殊检查新生认证时间窗口
    if (featureName === 'freshman_enabled' && !configStore.isInFreshmanWindow()) {
      ElMessage.error(configStore.getFeatureDisabledReason(featureName))
      next({ name: 'NotFound' })
      return
    }
  }

  // 检查认证状态
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复认证状态
      await authStore.checkAuth()
      
      if (!authStore.isAuthenticated) {
        ElMessage.warning('请先登录')
        next({
          name: 'AdminLogin',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      ElMessage.error('需要管理员权限')
      next({ name: 'Home' })
      return
    }
    
    // 检查具体权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
      ElMessage.error('权限不足')
      next({ name: 'Dashboard' })
      return
    }
  }
  
  // 已登录用户访问登录页面时重定向
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    if (authStore.isAdmin) {
      next({ name: 'Dashboard' })
    } else {
      next({ name: 'Home' })
    }
    return
  }
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 页面加载完成后的处理
  const authStore = useAuthStore()
  
  // 记录页面访问
  if (authStore.isAuthenticated) {
    authStore.recordPageVisit(to.path)
  }
})

export default router