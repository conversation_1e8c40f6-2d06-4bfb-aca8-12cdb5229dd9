<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维护模式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0369a1;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #d97706;
        }
    </style>
</head>
<body>
    <h1>维护模式功能测试</h1>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>点击"开启维护模式"按钮</li>
            <li>点击"访问认证页面"按钮（应该被重定向到维护页面）</li>
            <li>点击"关闭维护模式"按钮</li>
            <li>观察维护页面是否自动重定向回原页面</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>维护模式控制</h2>
        <button class="test-button" onclick="toggleMaintenance(true)">开启维护模式</button>
        <button class="test-button" onclick="toggleMaintenance(false)">关闭维护模式</button>
        <div id="maintenanceStatus" class="status"></div>
    </div>

    <div class="test-section">
        <h2>页面访问测试</h2>
        <button class="test-button" onclick="openPage('/auth/buaa')">访问本校学生认证</button>
        <button class="test-button" onclick="openPage('/auth/freshman')">访问新生认证</button>
        <button class="test-button" onclick="openPage('/auth/external')">访问外校学生认证</button>
        <button class="test-button" onclick="openPage('/')">访问主页</button>
        <button class="test-button" onclick="openPage('/admin')">访问管理后台</button>
    </div>

    <div class="test-section">
        <h2>维护页面直接测试</h2>
        <button class="test-button" onclick="openMaintenancePage('/auth/buaa')">模拟从本校认证进入维护页面</button>
        <button class="test-button" onclick="openMaintenancePage('/auth/freshman')">模拟从新生认证进入维护页面</button>
        <button class="test-button" onclick="openMaintenancePage('/')">模拟从主页进入维护页面</button>
    </div>

    <script>
        // 获取管理员token（假设已登录）
        const getAdminToken = () => {
            return localStorage.getItem('admin_token') || '';
        };

        // 切换维护模式
        async function toggleMaintenance(enabled) {
            const token = getAdminToken();
            if (!token) {
                updateStatus('maintenanceStatus', '请先登录管理员账户', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:5000/api/config/batch-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        configs: [
                            {
                                key: 'system.maintenance_mode',
                                value: enabled
                            }
                        ]
                    })
                });

                if (response.ok) {
                    updateStatus('maintenanceStatus', 
                        `维护模式已${enabled ? '开启' : '关闭'}`, 
                        'success');
                } else {
                    updateStatus('maintenanceStatus', 
                        `操作失败: ${response.status}`, 
                        'error');
                }
            } catch (error) {
                updateStatus('maintenanceStatus', 
                    `操作失败: ${error.message}`, 
                    'error');
            }
        }

        // 打开页面
        function openPage(path) {
            const url = `http://localhost:3000${path}`;
            window.open(url, '_blank');
        }

        // 打开维护页面（模拟重定向）
        function openMaintenancePage(redirectPath) {
            const url = `http://localhost:3000/maintenance?redirect=${encodeURIComponent(redirectPath)}`;
            window.open(url, '_blank');
        }

        // 更新状态显示
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 页面加载时检查当前维护模式状态
        async function checkMaintenanceStatus() {
            try {
                const response = await fetch('http://localhost:5000/api/config/public');
                if (response.ok) {
                    const data = await response.json();
                    const isMaintenanceMode = data.data.maintenance_mode;
                    updateStatus('maintenanceStatus', 
                        `当前维护模式: ${isMaintenanceMode ? '开启' : '关闭'}`, 
                        isMaintenanceMode ? 'warning' : 'success');
                }
            } catch (error) {
                updateStatus('maintenanceStatus', 
                    '无法获取维护模式状态', 
                    'error');
            }
        }

        // 页面加载时检查状态
        checkMaintenanceStatus();
    </script>
</body>
</html>
