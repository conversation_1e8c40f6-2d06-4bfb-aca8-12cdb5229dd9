<template>
  <div class="maintenance-page">
    <div class="maintenance-container">
      <div class="maintenance-content">
        <!-- 维护图标 -->
        <div class="maintenance-icon">
          <el-icon size="120" color="#E6A23C">
            <Tools />
          </el-icon>
        </div>

        <!-- 维护标题 -->
        <h1 class="maintenance-title">系统维护中</h1>

        <!-- 维护信息 -->
        <div class="maintenance-message">
          <p>{{ maintenanceMessage }}</p>
        </div>

        <!-- 系统信息 -->
        <div class="system-info">
          <p class="system-name">{{ siteName }}</p>
          <p class="contact-info">如有紧急问题，请联系管理员：{{ contactEmail }}</p>
        </div>

        <!-- 状态检查提示 -->
        <div class="status-check">
          <p class="check-info">
            <el-icon>
              <InfoFilled />
            </el-icon>
            维护结束后，请点击"刷新页面"按钮返回原页面
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="admin-actions">
          <el-button
            @click="refreshPage"
            :icon="Refresh"
            type="primary"
          >
            刷新页面
          </el-button>
          <el-button
            @click="closePage"
            :icon="Close"
          >
            关闭页面
          </el-button>
        </div>

        <!-- 返回首页按钮（如果维护模式关闭） -->
        <div class="back-home" v-if="!configs.maintenance_mode">
          <el-button
            type="success"
            @click="goHome"
            :icon="House"
          >
            返回首页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useConfigStore } from '@/stores/config'
import { Tools, Refresh, House, InfoFilled, Close } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const configStore = useConfigStore()

// 计算属性
const configs = computed(() => configStore.configs)
const siteName = computed(() => configs.value.site_name || '北航QQ身份认证系统')
const contactEmail = computed(() => configs.value.contact_email || '<EMAIL>')
const maintenanceMessage = computed(() => 
  configs.value.maintenance_message || '系统正在进行维护升级，暂时无法提供服务，请稍后再试。'
)

// 方法
const closePage = () => {
  // 跳转到空白页
  window.location.href = 'about:blank'
}

const refreshPage = async () => {
  // 先检查维护模式状态
  await checkMaintenanceStatus()

  // 如果维护模式仍然开启，则刷新页面
  if (configStore.configs.maintenance_mode) {
    window.location.reload()
  }
}

const goHome = () => {
  router.push('/')
}

// 检查维护模式状态并重定向
const checkMaintenanceStatus = async () => {
  // 重新加载配置
  await configStore.loadPublicConfigs()

  // 如果维护模式已关闭，立即跳转回原页面
  if (!configStore.configs.maintenance_mode) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
}

// 生命周期
onMounted(async () => {
  // 确保配置已加载
  if (!configStore.lastUpdated) {
    await configStore.init()
  }

  // 设置页面标题
  document.title = `系统维护中 - ${siteName.value}`

  // 立即检查维护模式状态
  await checkMaintenanceStatus()
})
</script>

<style scoped>
.maintenance-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.maintenance-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.maintenance-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
}

.maintenance-message {
  margin-bottom: 30px;
  font-size: 18px;
  color: #606266;
}

.system-info {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.system-name {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 10px;
}

.contact-info {
  font-size: 14px;
  color: #909399;
}

.status-check {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 14px;
  color: #0369a1;
}

.admin-actions {
  margin-bottom: 20px;
}

.admin-actions .el-button {
  margin: 5px;
}

.back-home {
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}


</style>
