/**
 * 主应用程序文件
 */

import Fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import multipart from '@fastify/multipart';
import { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';

import { prisma } from '@/config/database';
import { redisManager } from '@/config/redis';
import { logger } from '@/utils/logger';
import { configService } from '@/services/ConfigService';
import { config } from '@/config/env';

// 中间件
import { authenticateMiddleware } from '@/middleware/auth';
import { apiRateLimit } from '@/middleware/rateLimit';
import { securityMiddleware, securityHeaders } from '@/middleware/security';
import { auditMiddleware } from '@/middleware/audit';
import { contentSecurityValidation, createRequestSizeLimit } from '@/middleware/validation';
import { errorHand<PERSON>, notFoundHandler } from '@/middleware/errorHandler';
import { performanceMiddleware, systemMonitor } from '@/middleware/performance';

// 路由
import { authRoutes } from '@/routes/auth';
import { userRoutes } from '@/routes/users';
import { adminRoutes } from '@/routes/admin';
import { configRoutes } from '@/routes/config';
import { pendingRoutes } from '@/routes/pending';
import { inviteRoutes } from '@/routes/invites';
import { auditRoutes } from '@/routes/audit';
import { uploadRoutes } from '@/routes/upload';
import { statisticsRoutes } from '@/routes/statistics';
import monitoringRoutes from '@/routes/monitoring';

export async function createApp(): Promise<FastifyInstance> {
  // 创建Fastify实例
  const app = Fastify({
    logger: false, // 禁用Fastify内置logger，使用winston
    trustProxy: true,
  }).withTypeProvider<TypeBoxTypeProvider>();

  // 注册插件
  await registerPlugins(app);

  // 注册中间件
  await registerMiddleware(app);

  // 注册路由
  await registerRoutes(app);

  // 注册错误处理
  await registerErrorHandlers(app);

  // 注册生命周期钩子
  await registerLifecycleHooks(app);

  return app;
}

/**
 * 注册插件
 */
async function registerPlugins(app: FastifyInstance): Promise<void> {
  // CORS
  await app.register(cors, {
    origin: config.cors.origin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Request-ID', 'X-CSRF-Token'],
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  // 安全头
  await app.register(helmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  });

  // 全局速率限制
  await app.register(rateLimit, {
    max: 1000,
    timeWindow: '1 minute',
    redis: redisManager.getClient(),
  });

  // 文件上传
  await app.register(multipart, {
    limits: {
      fileSize: config.upload.maxSize,
      files: 5,
    },
  });
}

/**
 * 注册中间件
 */
async function registerMiddleware(app: FastifyInstance): Promise<void> {
  // 注册认证装饰器
  app.decorate('authenticate', authenticateMiddleware);

  // 全局安全中间件
  // app.addHook('onRequest', securityMiddleware);
  // app.addHook('onRequest', securityHeaders);
  // app.addHook('onRequest', contentSecurityValidation);
  // app.addHook('onRequest', createRequestSizeLimit(5 * 1024 * 1024)); // 5MB限制

  // 审计中间件
  // app.addHook('onRequest', auditMiddleware);

  // 性能监控中间件
  // app.addHook('onRequest', performanceMiddleware);

  // 请求日志
  app.addHook('onRequest', async (request, reply) => {
    logger.info({
      method: request.method,
      url: request.url,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
    }, 'Incoming request');
  });

  // 响应日志
  app.addHook('onSend', async (request, reply, payload) => {
    logger.info({
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      responseTime: reply.getResponseTime(),
    }, 'Request completed');

    return payload;
  });
}

/**
 * 注册路由
 */
async function registerRoutes(app: FastifyInstance): Promise<void> {
  // 健康检查
  app.get('/health', async (request, reply) => {
    try {
      // 检查数据库连接
      await prisma.$queryRaw`SELECT 1`;
      
      // 检查Redis连接
      await redisManager.healthCheck();
      
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          database: 'healthy',
          redis: 'healthy',
        },
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return reply.code(503).send({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Service unavailable',
      });
    }
  });

  // API版本信息
  app.get('/version', async (request, reply) => {
    return {
      name: 'BUAA Auth Backend (Node.js)',
      version: process.env.npm_package_version || '1.0.0',
      node: process.version,
      environment: config.app.env,
      timestamp: new Date().toISOString(),
    };
  });

  // 注册维护模式中间件
  const { registerMaintenanceMiddleware } = await import('./middleware/maintenance');
  registerMaintenanceMiddleware(app);

  // 注册API路由 - 逐步启用功能
  await app.register(authRoutes, { prefix: '/api/auth' });
  await app.register(userRoutes, { prefix: '/api/users' });
  await app.register(adminRoutes, { prefix: '/api/admin' });
  await app.register(configRoutes, { prefix: '/api/config' });

  // 添加简单的测试API
  app.get('/api/test', async (request, reply) => {
    return {
      success: true,
      message: 'Node.js backend is working!',
      timestamp: new Date().toISOString(),
      data: {
        server: 'Node.js + Fastify',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    };
  });




  await app.register(pendingRoutes, { prefix: '/api/pending' });
  await app.register(inviteRoutes, { prefix: '/api/invites' });
  await app.register(auditRoutes, { prefix: '/api/admin' });
  await app.register(uploadRoutes, { prefix: '/api/upload' });
  await app.register(statisticsRoutes, { prefix: '/api/admin' });
  await app.register(monitoringRoutes, { prefix: '/api/monitoring' });
}

/**
 * 注册错误处理
 */
async function registerErrorHandlers(app: FastifyInstance): Promise<void> {
  // 使用专用的错误处理器
  app.setErrorHandler(errorHandler);
}

/**
 * 注册生命周期钩子
 */
async function registerLifecycleHooks(app: FastifyInstance): Promise<void> {
  // 应用启动时的初始化
  app.addHook('onReady', async () => {
    logger.info('Application is ready');
    
    // 初始化默认配置
    try {
      await configService.initializeDefaultConfigs();
      logger.info('Default configurations initialized');
    } catch (error) {
      logger.error('Failed to initialize default configurations:', error);
    }

    // 启动系统监控
    try {
      systemMonitor.startMonitoring(60000); // 每分钟收集一次系统指标
      logger.info('System monitoring started');
    } catch (error) {
      logger.error('Failed to start system monitoring:', error);
    }
  });

  // 应用关闭时的清理
  app.addHook('onClose', async () => {
    logger.info('Application is shutting down');
    
    try {
      await prisma.$disconnect();
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
    }

    try {
      await redisManager.disconnect();
      logger.info('Redis connection closed');
    } catch (error) {
      logger.error('Error closing Redis connection:', error);
    }

    // 停止系统监控
    try {
      systemMonitor.stopMonitoring();
      logger.info('System monitoring stopped');
    } catch (error) {
      logger.error('Error stopping system monitoring:', error);
    }
  });
}

/**
 * 启动应用程序
 */
export async function startApp(): Promise<void> {
  try {
    const app = await createApp();

    await app.listen({ host: config.app.host, port: config.app.port });

    logger.info(`Server is running on http://${config.app.host}:${config.app.port}`);
    logger.info(`Environment: ${config.app.env}`);
  } catch (error) {
    logger.error('Failed to start application:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，启动应用
if (require.main === module) {
  startApp();
}
