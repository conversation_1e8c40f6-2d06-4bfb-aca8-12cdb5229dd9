/**
 * 配置服务 - 管理系统配置，支持数据库和Redis双重存储
 */

import { prisma } from '@/config/database';
import { redisManager } from '@/config/redis';
import { logger } from '@/utils/logger';
import { ConfigItem } from '@/types';

export class ConfigService {
  private static instance: ConfigService;
  private readonly configCacheKey = 'system_configs';
  private readonly cacheTTL = 3600; // 1小时

  // 默认配置定义
  private readonly defaultConfigs: Record<string, ConfigItem> = {
    // 认证相关配置
    'auth.buaa_enabled': {
      key: 'auth.buaa_enabled',
      value: true,
      description: '是否启用北航学生认证',
    },
    'auth.freshman_enabled': {
      key: 'auth.freshman_enabled',
      value: true,
      description: '是否启用新生认证',
    },
    'auth.external_enabled': {
      key: 'auth.external_enabled',
      value: true,
      description: '是否启用外部用户认证',
    },
    'auth.invite_enabled': {
      key: 'auth.invite_enabled',
      value: true,
      description: '是否启用邀请码认证',
    },
    'auth.freshman_available_dates': {
      key: 'auth.freshman_available_dates',
      value: ['2024-08-01', '2024-09-30'],
      description: '新生认证可用日期范围',
    },
    'auth.freshman_maintenance_windows': {
      key: 'auth.freshman_maintenance_windows',
      value: ['01:00-07:00'],
      description: '新生认证每日维护时间窗口',
    },

    // 验证码相关配置
    'verification.sms_enabled': {
      key: 'verification.sms_enabled',
      value: true,
      description: '是否启用短信验证码',
    },
    'verification.email_enabled': {
      key: 'verification.email_enabled',
      value: true,
      description: '是否启用邮箱验证码',
    },
    'verification.code_length': {
      key: 'verification.code_length',
      value: 6,
      description: '验证码长度',
    },
    'verification.code_expire_minutes': {
      key: 'verification.code_expire_minutes',
      value: 5,
      description: '验证码过期时间（分钟）',
    },
    'verification.max_attempts': {
      key: 'verification.max_attempts',
      value: 3,
      description: '验证码最大尝试次数',
    },

    // 安全相关配置
    'security.max_login_attempts': {
      key: 'security.max_login_attempts',
      value: 5,
      description: '最大登录尝试次数',
    },
    'security.lockout_duration_minutes': {
      key: 'security.lockout_duration_minutes',
      value: 30,
      description: '账户锁定时间（分钟）',
    },
    'security.jwt_expire_hours': {
      key: 'security.jwt_expire_hours',
      value: 24,
      description: 'JWT令牌过期时间（小时）',
    },
    'security.password_min_length': {
      key: 'security.password_min_length',
      value: 8,
      description: '密码最小长度',
    },

    // 文件上传配置
    'upload.max_file_size_mb': {
      key: 'upload.max_file_size_mb',
      value: 10,
      description: '最大文件上传大小（MB）',
    },
    'upload.allowed_types': {
      key: 'upload.allowed_types',
      value: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
      description: '允许的文件类型',
    },
    'upload.max_files_per_user': {
      key: 'upload.max_files_per_user',
      value: 5,
      description: '每个用户最大文件数量',
    },

    // 系统配置
    'system.site_name': {
      key: 'system.site_name',
      value: '北航QQ身份认证系统',
      description: '系统名称',
    },
    'system.site_description': {
      key: 'system.site_description',
      value: '北京航空航天大学QQ身份认证系统',
      description: '系统描述',
    },
    'system.contact_email': {
      key: 'system.contact_email',
      value: '<EMAIL>',
      description: '联系邮箱',
    },
    'system.maintenance_mode': {
      key: 'system.maintenance_mode',
      value: false,
      description: '维护模式',
    },
    'system.maintenance_message': {
      key: 'system.maintenance_message',
      value: '系统正在维护中，请稍后再试',
      description: '维护模式提示信息',
    },
    'system.registration_enabled': {
      key: 'system.registration_enabled',
      value: true,
      description: '是否允许注册',
    },
    'system.audit_log_retention_days': {
      key: 'system.audit_log_retention_days',
      value: 90,
      description: '审计日志保留天数',
    },

    // 通知配置
    'notification.email_enabled': {
      key: 'notification.email_enabled',
      value: true,
      description: '是否启用邮件通知',
    },
    'notification.sms_enabled': {
      key: 'notification.sms_enabled',
      value: true,
      description: '是否启用短信通知',
    },
    'notification.admin_email': {
      key: 'notification.admin_email',
      value: '<EMAIL>',
      description: '管理员邮箱',
    },

    // 限流配置
    'rate_limit.api_requests_per_minute': {
      key: 'rate_limit.api_requests_per_minute',
      value: 100,
      description: 'API每分钟请求限制',
    },
    'rate_limit.login_attempts_per_hour': {
      key: 'rate_limit.login_attempts_per_hour',
      value: 10,
      description: '每小时登录尝试限制',
    },
    'rate_limit.verification_codes_per_hour': {
      key: 'rate_limit.verification_codes_per_hour',
      value: 5,
      description: '每小时验证码发送限制',
    },
  };

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * 获取配置值
   */
  public async getConfig<T = any>(key: string, defaultValue?: T): Promise<T | null> {
    try {
      // 首先尝试从Redis缓存获取
      const cachedValue = await redisManager.hget(this.configCacheKey, key, true);
      if (cachedValue !== null) {
        return cachedValue as T;
      }

      // 如果Redis中没有，从数据库获取
      const config = await prisma.config.findUnique({
        where: { key },
      });

      if (config) {
        // 更新缓存
        await redisManager.hset(this.configCacheKey, key, config.value);
        await redisManager.expire(this.configCacheKey, this.cacheTTL);
        return config.value as T;
      }

      // 如果数据库中也没有，返回默认值
      if (defaultValue !== undefined) {
        return defaultValue;
      }

      // 检查是否有预定义的默认配置
      const defaultConfig = this.defaultConfigs[key];
      if (defaultConfig) {
        return defaultConfig.value as T;
      }

      return null;
    } catch (error) {
      logger.error(`Failed to get config ${key}:`, error);
      return defaultValue || null;
    }
  }

  /**
   * 设置配置值
   */
  public async setConfig(key: string, value: any, description?: string): Promise<void> {
    try {
      await prisma.config.upsert({
        where: { key },
        update: {
          value,
          description: description || undefined,
        },
        create: {
          key,
          value,
          description: description || this.defaultConfigs[key]?.description,
        },
      });

      // 更新缓存
      await redisManager.hset(this.configCacheKey, key, value);
      await redisManager.expire(this.configCacheKey, this.cacheTTL);

      logger.info(`Config updated: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
      logger.error(`Failed to set config ${key}:`, error);
      throw error;
    }
  }

  /**
   * 验证配置项
   */
  public async validateConfigs(configs: ConfigItem[]): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const errors: string[] = [];

      for (const config of configs) {
        // 验证必需字段
        if (!config.key || typeof config.key !== 'string') {
          errors.push(`配置项缺少有效的key: ${JSON.stringify(config)}`);
          continue;
        }

        if (config.value === undefined || config.value === null) {
          errors.push(`配置项 ${config.key} 缺少value`);
          continue;
        }

        // 验证key格式
        if (!/^[a-zA-Z0-9._-]+$/.test(config.key)) {
          errors.push(`配置项 ${config.key} 的key格式无效，只能包含字母、数字、点、下划线和连字符`);
          continue;
        }

        // 验证key长度
        if (config.key.length > 100) {
          errors.push(`配置项 ${config.key} 的key长度超过100个字符`);
          continue;
        }

        // 验证description长度
        if (config.description && config.description.length > 500) {
          errors.push(`配置项 ${config.key} 的description长度超过500个字符`);
          continue;
        }
      }

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      logger.error('Failed to validate configs:', error);
      return {
        valid: false,
        errors: ['配置验证过程中发生错误'],
      };
    }
  }

  /**
   * 批量更新配置
   */
  public async batchUpdateConfigs(configs: ConfigItem[]): Promise<{ success: boolean; updated: string[]; failed: string[]; errors?: string[] }> {
    const updated: string[] = [];
    const failed: string[] = [];
    const errors: string[] = [];

    try {
      await prisma.$transaction(async (tx) => {
        for (const config of configs) {
          try {
            await tx.config.upsert({
              where: { key: config.key },
              update: {
                value: config.value,
                description: config.description || undefined,
              },
              create: {
                key: config.key,
                value: config.value,
                description: config.description || this.defaultConfigs[config.key]?.description,
              },
            });
            updated.push(config.key);
          } catch (error) {
            failed.push(config.key);
            errors.push(`更新配置 ${config.key} 失败: ${error.message}`);
            logger.error(`Failed to update config ${config.key}:`, error);
          }
        }
      });

      // 批量更新缓存（只更新成功的配置）
      for (const config of configs) {
        if (updated.includes(config.key)) {
          try {
            await redisManager.hset(this.configCacheKey, config.key, config.value);
          } catch (error) {
            logger.error(`Failed to update cache for config ${config.key}:`, error);
          }
        }
      }

      if (updated.length > 0) {
        await redisManager.expire(this.configCacheKey, this.cacheTTL);
      }

      logger.info(`Batch update completed: ${updated.length} updated, ${failed.length} failed`);

      return {
        success: failed.length === 0,
        updated,
        failed,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      logger.error('Failed to batch update configs:', error);
      return {
        success: false,
        updated,
        failed: configs.map(c => c.key),
        errors: [`批量更新配置失败: ${error.message}`],
      };
    }
  }

  /**
   * 获取所有配置
   */
  public async getAllConfigs(): Promise<Record<string, any>> {
    try {
      // 尝试从缓存获取所有配置
      const cachedConfigs = await redisManager.hgetall(this.configCacheKey, true);
      if (Object.keys(cachedConfigs).length > 0) {
        return cachedConfigs;
      }

      // 从数据库获取所有配置
      const configs = await prisma.config.findMany();
      const configMap: Record<string, any> = {};

      for (const config of configs) {
        configMap[config.key] = config.value;
        // 更新缓存
        await redisManager.hset(this.configCacheKey, config.key, config.value);
      }

      await redisManager.expire(this.configCacheKey, this.cacheTTL);
      return configMap;
    } catch (error) {
      logger.error('Failed to get all configs:', error);
      throw error;
    }
  }

  /**
   * 获取所有配置（包含描述）
   */
  public async getAllConfigsWithDescription(): Promise<Record<string, ConfigItem>> {
    try {
      const configs = await prisma.config.findMany();
      const configMap: Record<string, ConfigItem> = {};

      for (const config of configs) {
        configMap[config.key] = {
          key: config.key,
          value: config.value,
          description: config.description || undefined,
        };
      }

      return configMap;
    } catch (error) {
      logger.error('Failed to get all configs with description:', error);
      throw error;
    }
  }

  /**
   * 删除配置
   */
  public async deleteConfig(key: string): Promise<void> {
    try {
      await prisma.config.delete({
        where: { key },
      });

      // 从缓存中删除 - 修复方法名
      const redis = redisManager.getClient();
      await redis.hdel(this.configCacheKey, key);

      logger.info(`Config deleted: ${key}`);
    } catch (error) {
      logger.error(`Failed to delete config ${key}:`, error);
      throw error;
    }
  }

  /**
   * 刷新配置缓存
   */
  public async refreshCache(): Promise<void> {
    try {
      // 清空缓存
      await redisManager.del(this.configCacheKey);

      // 重新加载所有配置到缓存
      await this.getAllConfigs();

      logger.info('Config cache refreshed');
    } catch (error) {
      logger.error('Failed to refresh config cache:', error);
      throw error;
    }
  }

  /**
   * 初始化默认配置
   */
  public async initializeDefaultConfigs(): Promise<void> {
    try {
      const existingConfigs = await prisma.config.findMany({
        select: { key: true },
      });
      const existingKeys = new Set(existingConfigs.map(c => c.key));

      const configsToCreate = Object.values(this.defaultConfigs)
        .filter(config => !existingKeys.has(config.key));

      if (configsToCreate.length > 0) {
        await prisma.config.createMany({
          data: configsToCreate.map(config => ({
            key: config.key,
            value: config.value,
            description: config.description,
          })),
        });

        logger.info(`Initialized ${configsToCreate.length} default configs`);
      }

      // 刷新缓存
      await this.refreshCache();
    } catch (error) {
      logger.error('Failed to initialize default configs:', error);
      throw error;
    }
  }

  /**
   * 获取布尔配置值（安全方法）
   */
  public async getBooleanConfig(key: string, defaultValue = false): Promise<boolean> {
    const value = await this.getConfig(key, defaultValue);
    return Boolean(value);
  }

  /**
   * 获取数字配置值（安全方法）
   */
  public async getNumberConfig(key: string, defaultValue = 0): Promise<number> {
    const value = await this.getConfig(key, defaultValue);
    return Number(value) || defaultValue;
  }

  /**
   * 获取字符串配置值（安全方法）
   */
  public async getStringConfig(key: string, defaultValue = ''): Promise<string> {
    const value = await this.getConfig(key, defaultValue);
    return String(value) || defaultValue;
  }

  /**
   * 获取数组配置值（安全方法）
   */
  public async getArrayConfig<T = any>(key: string, defaultValue: T[] = []): Promise<T[]> {
    const value = await this.getConfig(key, defaultValue);
    return Array.isArray(value) ? value : defaultValue;
  }

  /**
   * 清除配置缓存
   */
  public async clearCache(): Promise<void> {
    try {
      await redisManager.del(this.configCacheKey);
      logger.info('Configuration cache cleared');
    } catch (error) {
      logger.error('Failed to clear configuration cache:', error);
      throw error;
    }
  }

  /**
   * 预热缓存
   */
  public async warmupCache(): Promise<void> {
    try {
      // 获取所有配置并缓存
      const configs = await prisma.config.findMany();

      if (configs.length > 0) {
        // 使用hset逐个设置配置项
        for (const config of configs) {
          await redisManager.hset(this.configCacheKey, config.key, config.value);
        }

        await redisManager.expire(this.configCacheKey, this.cacheTTL);

        logger.info(`Warmed up cache with ${configs.length} configurations`);
      }
    } catch (error) {
      logger.error('Failed to warmup configuration cache:', error);
      throw error;
    }
  }

  /**
   * 设置缓存配置（仅缓存，不写数据库）
   */
  public async setCacheConfig(key: string, value: any): Promise<void> {
    try {
      await redisManager.hset(this.configCacheKey, key, value);
      await redisManager.expire(this.configCacheKey, this.cacheTTL);
    } catch (error) {
      logger.error(`Failed to set cache config ${key}:`, error);
      throw error;
    }
  }
}

// 导出单例实例
export const configService = ConfigService.getInstance();
